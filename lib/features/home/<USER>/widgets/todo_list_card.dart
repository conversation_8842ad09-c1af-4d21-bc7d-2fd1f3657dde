import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../../core/theme/app_theme.dart';

class TodoItem {
  final String id;
  final String title;
  final String time;
  final IconData icon;
  final Color iconColor;
  bool isCompleted;

  TodoItem({
    required this.id,
    required this.title,
    required this.time,
    required this.icon,
    required this.iconColor,
    this.isCompleted = false,
  });
}

class TodoListCard extends StatefulWidget {
  const TodoListCard({super.key});

  @override
  State<TodoListCard> createState() => _TodoListCardState();
}

class _TodoListCardState extends State<TodoListCard> {
  List<TodoItem> todoItems = [
    TodoItem(
      id: '1',
      title: '给绿萝浇水',
      time: '今天 10:00',
      icon: Icons.water_drop,
      iconColor: AppTheme.blue500,
    ),
    TodoItem(
      id: '2',
      title: '给多肉松土',
      time: '今天 14:30',
      icon: MdiIcons.leaf,
      iconColor: AppTheme.green500,
    ),
    TodoItem(
      id: '3',
      title: '给绿萝施肥',
      time: '今天 16:00',
      icon: MdiIcons.sprout,
      iconColor: AppTheme.primary,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '今日待办',
              style: AppTheme.heading3,
            ),
            TextButton(
              onPressed: () {
                // TODO: 导航到全部待办页面
              },
              child: const Text(
                '查看全部',
                style: TextStyle(
                  color: AppTheme.primary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surface,
            borderRadius: AppTheme.cardRadius,
            boxShadow: const [AppTheme.cardShadow],
          ),
          child: Column(
            children: [
              if (todoItems.isEmpty)
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 24),
                  child: Text(
                    '今日没有待办事项',
                    style: TextStyle(
                      color: AppTheme.gray500,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                )
              else
                ...todoItems.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    opacity: item.isCompleted ? 0.5 : 1.0,
                    child: Column(
                      children: [
                        _buildTodoItem(item),
                        if (index < todoItems.length - 1)
                          const Divider(
                            color: AppTheme.gray100,
                            height: 1,
                          ),
                      ],
                    ),
                  );
                }).toList(),
              
              const SizedBox(height: 12),
              InkWell(
                onTap: () {
                  // TODO: 添加新待办
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add,
                        color: AppTheme.primary,
                        size: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        '添加新待办',
                        style: TextStyle(
                          color: AppTheme.primary,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTodoItem(TodoItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // 图标
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: item.iconColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              item.icon,
              color: item.iconColor,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          
          // 内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.gray800,
                    decoration: item.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
                Text(
                  item.time,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.gray500,
                  ),
                ),
              ],
            ),
          ),
          
          // 完成按钮
          InkWell(
            onTap: () {
              setState(() {
                item.isCompleted = !item.isCompleted;
              });
              
              if (item.isCompleted) {
                // 2秒后移除已完成的任务
                Future.delayed(const Duration(seconds: 2), () {
                  if (mounted) {
                    setState(() {
                      todoItems.removeWhere((todo) => todo.id == item.id);
                    });
                  }
                });
              }
            },
            borderRadius: BorderRadius.circular(16),
            child: Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: Icon(
                item.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                color: item.isCompleted ? AppTheme.primary : AppTheme.gray400,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
