import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import 'tables/plants_table.dart';
import 'tables/care_records_table.dart';
import 'tables/reminders_table.dart';
import 'tables/photos_table.dart';
import 'tables/health_data_table.dart';

part 'database.g.dart';

@DriftDatabase(tables: [
  Plants,
  CareRecords,
  Reminders,
  Photos,
  HealthData,
])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
        await _insertDefaultCategories();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // 处理数据库升级
      },
    );
  }

  // 插入默认植物分类
  Future<void> _insertDefaultCategories() async {
    final defaultCategories = [
      PlantCategoriesCompanion.insert(
        name: '多肉植物',
        description: '耐旱、肉质叶片的植物',
        careLevel: 'beginner',
        defaultWateringInterval: 7,
        defaultLightRequirement: 'bright_indirect',
      ),
      PlantCategoriesCompanion.insert(
        name: '观叶植物',
        description: '以叶片观赏为主的植物',
        careLevel: 'intermediate',
        defaultWateringInterval: 3,
        defaultLightRequirement: 'indirect',
      ),
      PlantCategoriesCompanion.insert(
        name: '开花植物',
        description: '以花朵观赏为主的植物',
        careLevel: 'advanced',
        defaultWateringInterval: 2,
        defaultLightRequirement: 'bright_direct',
      ),
      PlantCategoriesCompanion.insert(
        name: '草本植物',
        description: '草本类观赏植物',
        careLevel: 'intermediate',
        defaultWateringInterval: 2,
        defaultLightRequirement: 'bright_indirect',
      ),
      PlantCategoriesCompanion.insert(
        name: '木本植物',
        description: '木本类观赏植物',
        careLevel: 'advanced',
        defaultWateringInterval: 4,
        defaultLightRequirement: 'bright_direct',
      ),
    ];

    for (final category in defaultCategories) {
      await into(plantCategories).insert(category);
    }
  }

  // 植物相关查询
  Future<List<Plant>> getAllPlants() => select(plants).get();

  Future<Plant?> getPlantById(String id) =>
      (select(plants)..where((p) => p.id.equals(id))).getSingleOrNull();

  Future<List<Plant>> getPlantsByCategory(int categoryId) =>
      (select(plants)..where((p) => p.categoryId.equals(categoryId))).get();

  Future<List<Plant>> searchPlants(String query) => (select(plants)
        ..where((p) => p.name.contains(query) | p.nickname.contains(query)))
      .get();

  // 养护记录相关查询
  Future<List<CareRecord>> getCareRecordsByPlant(String plantId) =>
      (select(careRecords)
            ..where((c) => c.plantId.equals(plantId))
            ..orderBy([(c) => OrderingTerm.desc(c.recordDate)]))
          .get();

  Future<List<CareRecord>> getRecentCareRecords({int limit = 10}) =>
      (select(careRecords)
            ..orderBy([(c) => OrderingTerm.desc(c.recordDate)])
            ..limit(limit))
          .get();

  // 提醒相关查询
  Future<List<Reminder>> getActiveReminders() =>
      (select(reminders)..where((r) => r.isActive.equals(true))).get();

  Future<List<Reminder>> getRemindersByPlant(String plantId) =>
      (select(reminders)..where((r) => r.plantId.equals(plantId))).get();

  // 照片相关查询
  Future<List<Photo>> getPhotosByPlant(String plantId) => (select(photos)
        ..where((p) => p.plantId.equals(plantId))
        ..orderBy([(p) => OrderingTerm.desc(p.takenAt)]))
      .get();

  // 健康数据相关查询
  Future<List<HealthDatum>> getHealthDataByPlant(String plantId) =>
      (select(healthData)
            ..where((h) => h.plantId.equals(plantId))
            ..orderBy([(h) => OrderingTerm.desc(h.recordDate)]))
          .get();

  // 分类相关查询
  Future<List<PlantCategory>> getAllCategories() =>
      select(plantCategories).get();
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'plant_care.db'));
    return NativeDatabase.createInBackground(file);
  });
}
