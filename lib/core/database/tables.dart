import 'package:drift/drift.dart';

// 植物分类表
@DataClassName('PlantCategory')
class PlantCategories extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 50)();
  TextColumn get description => text().nullable()();
  TextColumn get careLevel => text()(); // beginner, intermediate, advanced

  // 默认养护参数
  IntColumn get defaultWateringInterval =>
      integer().withDefault(const Constant(7))();
  TextColumn get defaultLightRequirement =>
      text()(); // low, indirect, bright_indirect, bright_direct
  IntColumn get defaultFertilizingInterval =>
      integer().withDefault(const Constant(30))();

  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// 植物表
@DataClassName('Plant')
class Plants extends Table {
  TextColumn get id => text()(); // UUID

  // 基本信息
  TextColumn get name => text().withLength(min: 1, max: 100)(); // 植物学名或品种名
  TextColumn get nickname => text().withLength(min: 1, max: 50)(); // 用户昵称
  TextColumn get description => text().nullable()(); // 植物描述

  // 分类信息
  IntColumn get categoryId =>
      integer().nullable().references(PlantCategories, #id)();
  TextColumn get tags => text().nullable()(); // JSON格式存储标签数组

  // 购买/种植信息
  DateTimeColumn get acquiredDate => dateTime()(); // 获得日期
  TextColumn get acquiredFrom => text().nullable()(); // 获得来源
  RealColumn get purchasePrice => real().nullable()(); // 购买价格

  // 位置信息
  TextColumn get location => text().withLength(max: 100)(); // 摆放位置
  TextColumn get environment =>
      text()(); // 生长环境: indoor, outdoor, balcony, greenhouse

  // 养护参数
  IntColumn get wateringInterval =>
      integer().withDefault(const Constant(7))(); // 浇水间隔（天）
  TextColumn get lightRequirement => text()(); // 光照需求
  IntColumn get fertilizingInterval =>
      integer().withDefault(const Constant(30))(); // 施肥间隔（天）

  // 生长信息
  RealColumn get currentHeight => real().nullable()(); // 当前高度（cm）
  RealColumn get currentWidth => real().nullable()(); // 当前宽度（cm）
  TextColumn get growthStage => text()(); // 生长阶段

  // 健康状态
  IntColumn get healthScore =>
      integer().withDefault(const Constant(100))(); // 健康评分 0-100
  TextColumn get healthStatus => text()(); // 健康状态

  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();

  // 同步字段
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  TextColumn get syncStatus =>
      text().withDefault(const Constant('local'))(); // local, synced, pending

  @override
  Set<Column> get primaryKey => {id};
}

// 养护记录表
@DataClassName('CareRecord')
class CareRecords extends Table {
  TextColumn get id => text()(); // UUID
  TextColumn get plantId => text().references(Plants, #id)();

  // 养护类型
  TextColumn get careType =>
      text()(); // watering, fertilizing, pruning, repotting, pest_control, health_check, other

  // 记录时间
  DateTimeColumn get recordDate => dateTime()();

  // 养护详情（JSON格式存储不同类型的具体信息）
  TextColumn get details => text()(); // JSON格式
  TextColumn get notes => text().nullable()(); // 备注
  TextColumn get mood => text().nullable()(); // 植物状态评价

  // 浇水相关字段
  TextColumn get waterAmount => text().nullable()(); // light, moderate, heavy
  TextColumn get waterType =>
      text().nullable()(); // tap, filtered, rainwater, distilled

  // 施肥相关字段
  TextColumn get fertilizerType => text().nullable()(); // 肥料类型
  RealColumn get fertilizerAmount => real().nullable()(); // 施肥量
  TextColumn get fertilizerMethod =>
      text().nullable()(); // root, foliar, slow_release

  // 环境数据
  RealColumn get temperature => real().nullable()(); // 温度
  RealColumn get humidity => real().nullable()(); // 湿度
  IntColumn get lightHours => integer().nullable()(); // 光照时长

  // 效果评估
  IntColumn get effectivenessScore => integer().nullable()(); // 效果评分 1-5

  // 关联照片
  TextColumn get photoIds => text().nullable()(); // JSON数组，存储相关照片ID

  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  // 同步字段
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  TextColumn get syncStatus => text().withDefault(const Constant('local'))();

  @override
  Set<Column> get primaryKey => {id};
}

// 提醒表
@DataClassName('Reminder')
class Reminders extends Table {
  TextColumn get id => text()(); // UUID
  TextColumn get plantId => text().references(Plants, #id)();

  // 提醒类型
  TextColumn get reminderType =>
      text()(); // watering, fertilizing, pruning, repotting, pest_check, health_check, seasonal, custom

  // 提醒标题和内容
  TextColumn get title => text().withLength(min: 1, max: 100)();
  TextColumn get message => text().nullable()();

  // 时间设置
  DateTimeColumn get nextReminderTime => dateTime()(); // 下次提醒时间
  IntColumn get intervalDays => integer().nullable()(); // 提醒间隔（天）
  TextColumn get reminderTime => text().nullable()(); // 每日提醒时间 (HH:mm格式)

  // 重复设置
  BoolColumn get isRepeating =>
      boolean().withDefault(const Constant(true))(); // 是否重复
  TextColumn get repeatPattern =>
      text().nullable()(); // 重复模式 (daily, weekly, monthly, custom)

  // 状态
  BoolColumn get isActive =>
      boolean().withDefault(const Constant(true))(); // 是否激活
  BoolColumn get isCompleted =>
      boolean().withDefault(const Constant(false))(); // 是否已完成
  DateTimeColumn get lastTriggeredAt => dateTime().nullable()(); // 上次触发时间
  DateTimeColumn get completedAt => dateTime().nullable()(); // 完成时间

  // 智能调整
  BoolColumn get enableSmartAdjustment =>
      boolean().withDefault(const Constant(true))(); // 是否启用智能调整

  // 优先级
  IntColumn get priority =>
      integer().withDefault(const Constant(1))(); // 优先级 1-5

  // 通知设置
  BoolColumn get enableNotification =>
      boolean().withDefault(const Constant(true))(); // 是否启用通知
  IntColumn get notificationAdvanceMinutes =>
      integer().withDefault(const Constant(0))(); // 提前通知分钟数

  // 延迟设置
  IntColumn get snoozeCount =>
      integer().withDefault(const Constant(0))(); // 延迟次数
  IntColumn get maxSnoozeCount =>
      integer().withDefault(const Constant(3))(); // 最大延迟次数

  // 统计信息
  IntColumn get totalTriggered =>
      integer().withDefault(const Constant(0))(); // 总触发次数
  IntColumn get totalCompleted =>
      integer().withDefault(const Constant(0))(); // 总完成次数

  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();

  // 同步字段
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  TextColumn get syncStatus => text().withDefault(const Constant('local'))();

  @override
  Set<Column> get primaryKey => {id};
}

// 照片表
@DataClassName('Photo')
class Photos extends Table {
  TextColumn get id => text()(); // UUID
  TextColumn get plantId => text().references(Plants, #id)();
  TextColumn get careRecordId =>
      text().nullable().references(CareRecords, #id)();

  // 文件信息
  TextColumn get fileName => text()(); // 文件名
  TextColumn get filePath => text()(); // 本地文件路径
  TextColumn get fileUrl => text().nullable()(); // 云端文件URL（同步后）
  IntColumn get fileSize => integer()(); // 文件大小（字节）
  TextColumn get mimeType =>
      text().withDefault(const Constant('image/jpeg'))(); // MIME类型

  // 图片信息
  IntColumn get width => integer().nullable()(); // 图片宽度
  IntColumn get height => integer().nullable()(); // 图片高度

  // 拍摄信息
  DateTimeColumn get takenAt => dateTime()(); // 拍摄时间
  TextColumn get location => text().nullable()(); // 拍摄位置

  // 照片类型和用途
  TextColumn get photoType =>
      text()(); // growth, health, care, problem, before_after, identification, general

  // 照片标签和描述
  TextColumn get title => text().nullable()(); // 照片标题
  TextColumn get description => text().nullable()(); // 照片描述
  TextColumn get tags => text().nullable()(); // 标签 (JSON数组)

  // 植物部位
  TextColumn get plantPart => text()
      .nullable()(); // whole_plant, leaves, stem, roots, flowers, fruits, buds, new_growth, problem_area

  // 生长阶段
  TextColumn get growthStage => text()
      .nullable()(); // seedling, juvenile, mature, flowering, fruiting, dormant

  // 照片质量和状态
  IntColumn get qualityScore => integer().nullable()(); // 质量评分 1-5
  BoolColumn get isFavorite =>
      boolean().withDefault(const Constant(false))(); // 是否收藏
  BoolColumn get isProfilePhoto =>
      boolean().withDefault(const Constant(false))(); // 是否为植物头像
  BoolColumn get isPublic =>
      boolean().withDefault(const Constant(false))(); // 是否公开（社区分享）

  // AI分析结果
  TextColumn get aiAnalysis => text().nullable()(); // AI分析结果 (JSON格式)
  RealColumn get healthScore => real().nullable()(); // AI评估的健康分数
  TextColumn get identifiedSpecies => text().nullable()(); // AI识别的物种
  RealColumn get identificationConfidence => real().nullable()(); // 识别置信度

  // 处理状态
  BoolColumn get isProcessed =>
      boolean().withDefault(const Constant(false))(); // 是否已处理
  BoolColumn get isCompressed =>
      boolean().withDefault(const Constant(false))(); // 是否已压缩

  // 缩略图
  TextColumn get thumbnailPath => text().nullable()(); // 缩略图路径
  TextColumn get thumbnailUrl => text().nullable()(); // 缩略图URL

  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();

  // 同步字段
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  TextColumn get syncStatus => text().withDefault(const Constant('local'))();
  BoolColumn get needsUpload =>
      boolean().withDefault(const Constant(true))(); // 是否需要上传

  @override
  Set<Column> get primaryKey => {id};
}

// 健康数据表
@DataClassName('HealthDatum')
class HealthData extends Table {
  TextColumn get id => text()(); // UUID
  TextColumn get plantId => text().references(Plants, #id)();
  TextColumn get careRecordId =>
      text().nullable().references(CareRecords, #id)();

  // 记录时间
  DateTimeColumn get recordDate => dateTime()();

  // 整体健康评估
  IntColumn get overallHealthScore => integer()(); // 总体健康分数 0-100
  TextColumn get healthStatus =>
      text()(); // excellent, good, fair, poor, critical

  // 生长指标
  RealColumn get height => real().nullable()(); // 高度 (cm)
  RealColumn get width => real().nullable()(); // 宽度 (cm)
  IntColumn get leafCount => integer().nullable()(); // 叶片数量
  IntColumn get newGrowthCount => integer().nullable()(); // 新芽数量
  RealColumn get stemDiameter => real().nullable()(); // 茎干直径 (mm)

  // 叶片健康
  IntColumn get leafHealthScore => integer().nullable()(); // 叶片健康分数
  TextColumn get leafColor => text().nullable()(); // 叶片颜色描述
  TextColumn get leafTexture =>
      text().nullable()(); // normal, wilted, crispy, soft, spotted
  IntColumn get yellowLeafCount => integer().nullable()(); // 黄叶数量
  IntColumn get brownLeafCount => integer().nullable()(); // 褐叶数量
  IntColumn get droppedLeafCount => integer().nullable()(); // 掉叶数量

  // 茎干健康
  IntColumn get stemHealthScore => integer().nullable()(); // 茎干健康分数
  TextColumn get stemColor => text().nullable()(); // 茎干颜色
  TextColumn get stemCondition =>
      text().nullable()(); // firm, soft, woody, flexible, brittle
  BoolColumn get hasNewGrowth => boolean().nullable()(); // 是否有新芽

  // 根系健康（换盆时记录）
  IntColumn get rootHealthScore => integer().nullable()(); // 根系健康分数
  TextColumn get rootColor => text().nullable()(); // 根系颜色
  TextColumn get rootCondition =>
      text().nullable()(); // healthy, dry, rotted, bound, sparse
  BoolColumn get hasNewRoots => boolean().nullable()(); // 是否有新根

  // 开花结果（如适用）
  IntColumn get flowerCount => integer().nullable()(); // 花朵数量
  IntColumn get budCount => integer().nullable()(); // 花蕾数量
  IntColumn get fruitCount => integer().nullable()(); // 果实数量
  TextColumn get floweringStage =>
      text().nullable()(); // budding, blooming, fading, finished

  // 病虫害记录
  BoolColumn get hasPests =>
      boolean().withDefault(const Constant(false))(); // 是否有害虫
  BoolColumn get hasDisease =>
      boolean().withDefault(const Constant(false))(); // 是否有病害
  TextColumn get pestTypes => text().nullable()(); // 害虫类型 (JSON数组)
  TextColumn get diseaseTypes => text().nullable()(); // 病害类型 (JSON数组)
  IntColumn get pestSeverity => integer().nullable()(); // 害虫严重程度 1-5
  IntColumn get diseaseSeverity => integer().nullable()(); // 病害严重程度 1-5

  // 环境适应性
  IntColumn get environmentAdaptation => integer().nullable()(); // 环境适应性 1-5
  BoolColumn get showsStress =>
      boolean().withDefault(const Constant(false))(); // 是否显示压力症状
  TextColumn get stressSymptoms => text().nullable()(); // 压力症状 (JSON数组)

  // 养护响应
  IntColumn get wateringResponse => integer().nullable()(); // 对浇水的响应 1-5
  IntColumn get fertilizingResponse => integer().nullable()(); // 对施肥的响应 1-5
  IntColumn get lightResponse => integer().nullable()(); // 对光照的响应 1-5

  // AI分析结果
  TextColumn get aiAnalysisResult => text().nullable()(); // AI分析结果 (JSON格式)
  RealColumn get aiConfidenceScore => real().nullable()(); // AI分析置信度
  TextColumn get aiRecommendations => text().nullable()(); // AI建议 (JSON数组)

  // 照片关联
  TextColumn get photoIds => text().nullable()(); // 相关照片ID (JSON数组)

  // 备注
  TextColumn get notes => text().nullable()(); // 备注
  TextColumn get observerNotes => text().nullable()(); // 观察者备注

  // 数据来源
  TextColumn get dataSource => text()(); // manual, ai_analysis, sensor, expert

  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  // 同步字段
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  TextColumn get syncStatus => text().withDefault(const Constant('local'))();

  @override
  Set<Column> get primaryKey => {id};
}
