import 'package:drift/drift.dart';

@DataClassName('Reminder')
class Reminders extends Table {
  // 主键
  TextColumn get id => text()();
  
  // 关联植物
  TextColumn get plantId => text().references(Plants, #id)();
  
  // 提醒类型
  TextColumn get reminderType => text().check(reminderType.isIn([
    'watering',      // 浇水提醒
    'fertilizing',   // 施肥提醒
    'pruning',       // 修剪提醒
    'repotting',     // 换盆提醒
    'pest_check',    // 病虫害检查
    'health_check',  // 健康检查
    'seasonal',      // 季节性提醒
    'custom'         // 自定义提醒
  ]))();
  
  // 提醒标题和内容
  TextColumn get title => text().withLength(min: 1, max: 100)();
  TextColumn get message => text().nullable()();
  
  // 时间设置
  DateTimeColumn get nextReminderTime => dateTime()(); // 下次提醒时间
  IntColumn get intervalDays => integer().nullable()(); // 提醒间隔（天）
  TextColumn get reminderTime => text().nullable()(); // 每日提醒时间 (HH:mm格式)
  
  // 重复设置
  BoolColumn get isRepeating => boolean().withDefault(const Constant(true))(); // 是否重复
  TextColumn get repeatPattern => text().nullable()(); // 重复模式 (daily, weekly, monthly, custom)
  TextColumn get customPattern => text().nullable()(); // 自定义重复模式 (JSON格式)
  
  // 状态
  BoolColumn get isActive => boolean().withDefault(const Constant(true))(); // 是否激活
  BoolColumn get isCompleted => boolean().withDefault(const Constant(false))(); // 是否已完成
  DateTimeColumn get lastTriggeredAt => dateTime().nullable()(); // 上次触发时间
  DateTimeColumn get completedAt => dateTime().nullable()(); // 完成时间
  
  // 智能调整
  BoolColumn get enableSmartAdjustment => boolean().withDefault(const Constant(true))(); // 是否启用智能调整
  TextColumn get adjustmentFactors => text().nullable()(); // 调整因子 (JSON格式，包含天气、季节等)
  
  // 优先级
  IntColumn get priority => integer().withDefault(const Constant(1)).check(priority.isBetweenValues(1, 5))(); // 优先级 1-5
  
  // 通知设置
  BoolColumn get enableNotification => boolean().withDefault(const Constant(true))(); // 是否启用通知
  IntColumn get notificationAdvanceMinutes => integer().withDefault(const Constant(0))(); // 提前通知分钟数
  BoolColumn get enableSound => boolean().withDefault(const Constant(true))(); // 是否启用声音
  BoolColumn get enableVibration => boolean().withDefault(const Constant(true))(); // 是否启用震动
  
  // 延迟设置
  IntColumn get snoozeCount => integer().withDefault(const Constant(0))(); // 延迟次数
  IntColumn get maxSnoozeCount => integer().withDefault(const Constant(3))(); // 最大延迟次数
  IntColumn get snoozeIntervalMinutes => integer().withDefault(const Constant(30))(); // 延迟间隔分钟数
  
  // 条件设置
  TextColumn get conditions => text().nullable()(); // 触发条件 (JSON格式)
  
  // 统计信息
  IntColumn get totalTriggered => integer().withDefault(const Constant(0))(); // 总触发次数
  IntColumn get totalCompleted => integer().withDefault(const Constant(0))(); // 总完成次数
  IntColumn get totalSnoozed => integer().withDefault(const Constant(0))(); // 总延迟次数
  
  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();
  
  // 同步字段
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  TextColumn get syncStatus => text().withDefault(const Constant('local'))();
  
  @override
  Set<Column> get primaryKey => {id};
}
