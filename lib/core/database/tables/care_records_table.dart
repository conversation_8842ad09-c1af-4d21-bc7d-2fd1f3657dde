import 'package:drift/drift.dart';

@DataClassName('CareRecord')
class CareRecords extends Table {
  // 主键
  TextColumn get id => text()();

  // 关联植物
  TextColumn get plantId => text().references(Plants, #id)();

  // 养护类型
  TextColumn get careType =>
      text()(); // watering, fertilizing, pruning, repotting, pest_control, light_adjustment, health_check, other

  // 记录时间
  DateTimeColumn get recordDate => dateTime()();

  // 养护详情（JSON格式存储不同类型的具体信息）
  TextColumn get details => text()(); // JSON格式

  // 通用字段
  TextColumn get notes => text().nullable()(); // 备注
  TextColumn get mood => text().nullable().check(mood
      .isIn(['excellent', 'good', 'normal', 'concerning', 'poor']))(); // 植物状态评价

  // 浇水相关字段
  TextColumn get waterAmount => text()
      .nullable()
      .check(waterAmount.isIn(['light', 'moderate', 'heavy']))(); // 浇水量
  TextColumn get waterType => text().nullable().check(
      waterType.isIn(['tap', 'filtered', 'rainwater', 'distilled']))(); // 水质类型

  // 施肥相关字段
  TextColumn get fertilizerType => text().nullable()(); // 肥料类型
  RealColumn get fertilizerAmount => real().nullable()(); // 施肥量
  TextColumn get fertilizerMethod => text().nullable().check(
      fertilizerMethod.isIn(['root', 'foliar', 'slow_release']))(); // 施肥方式

  // 修剪相关字段
  TextColumn get pruningType => text().nullable().check(pruningType.isIn(
      ['deadheading', 'shaping', 'disease_removal', 'propagation']))(); // 修剪类型
  TextColumn get pruningParts => text().nullable()(); // 修剪部位

  // 换盆相关字段
  TextColumn get potSize => text().nullable()(); // 花盆尺寸
  TextColumn get soilType => text().nullable()(); // 土壤类型
  TextColumn get repottingReason => text().nullable()(); // 换盆原因

  // 病虫害防治相关字段
  TextColumn get pestType => text().nullable()(); // 病虫害类型
  TextColumn get treatmentMethod => text().nullable()(); // 治疗方法
  TextColumn get treatmentProduct => text().nullable()(); // 使用的产品

  // 环境数据
  RealColumn get temperature => real().nullable()(); // 温度
  RealColumn get humidity => real().nullable()(); // 湿度
  IntColumn get lightHours => integer().nullable()(); // 光照时长

  // 效果评估
  IntColumn get effectivenessScore => integer()
      .nullable()
      .check(effectivenessScore.isBetweenValues(1, 5))(); // 效果评分 1-5

  // 关联照片
  TextColumn get photoIds => text().nullable()(); // JSON数组，存储相关照片ID

  // 系统字段
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  // 同步字段
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  TextColumn get syncStatus => text().withDefault(const Constant('local'))();

  @override
  Set<Column> get primaryKey => {id};
}
